<template>
  <el-drawer
    class="detail-drawer"
    :title="$T('详情')"
    :visible.sync="drawerVisible"
    destroy-on-close
    size="960px"
  >
    <div class="drawer-content">
      <!-- 基础信息 -->
      <div class="basic-info-section">
        <el-row :gutter="24">
          <el-col :span="12">
            <div class="info-item">
              <div class="label">{{ $T('机组名称') }}</div>
              <div class="value">{{ detailData.name || "--" }}</div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <div class="label">{{ $T('机组类型') }}</div>
              <div class="value">{{ detailData.type || "--" }}</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 资源列表 -->
      <div class="resource-list-section">
        <div class="section-title">{{ $T('调峰机组资源列表') }}</div>
        
        <!-- 资源表格 -->
        <div class="resource-table-wrapper">
          <el-table
            :data="currentPageResourceData"
            class="resource-table"
            max-height="440"
          >
            <el-table-column
              prop="index"
              :label="$T('序号')"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                {{ String(scope.row.index).padStart(2, '0') }}
              </template>
            </el-table-column>

            <el-table-column
              prop="resourceId"
              :label="$T('资源ID')"
              min-width="180"
            />

            <el-table-column
              prop="resourceName"
              :label="$T('资源名称')"
              min-width="200"
            />

            <el-table-column
              prop="region"
              :label="$T('区域')"
              min-width="120"
            />

            <el-table-column
              prop="capacity"
              :label="$T('报装容量（kVA）')"
              min-width="140"
            />

            <el-table-column
              prop="directControl"
              :label="$T('平台直控')"
              min-width="100"
            >
              <template slot-scope="scope">
                {{ scope.row.directControl ? $T('是') : $T('否') }}
              </template>
            </el-table-column>

            <el-table-column
              :label="$T('操作')"
              width="100"
            >
              <template slot-scope="scope">
                <el-button
                  type="text"
                  class="unbind-btn"
                  @click="handleUnbind(scope.row)"
                >
                  {{ $T('解绑') }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页器 -->
        <div class="pagination-wrapper">
          <div class="pagination-info">
            <span class="total-text">
              {{ $T('共') }}<span class="total-number">{{ totalResourceCount }}</span>{{ $T('个') }}
            </span>
          </div>
          
          <div class="pagination-controls">
            <div class="page-size-selector">
              <el-select v-model="pageSize" class="page-size-select" @change="handlePageSizeChange">
                <el-option
                  v-for="size in pageSizeOptions"
                  :key="size"
                  :label="`${size}${$T('条/页')}`"
                  :value="size"
                />
              </el-select>
            </div>

            <el-pagination
              :current-page="currentPage"
              :page-size="pageSize"
              :total="totalResourceCount"
              layout="prev, pager, next, jumper"
              @current-change="handlePageChange"
              class="pagination-component"
            />
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script>
export default {
  name: 'DetailDrawer',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    detailData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 分页相关
      currentPage: 1,
      pageSize: 10,
      pageSizeOptions: [10, 20, 50, 100],
      
      // 资源数据
      allResourceData: [],
      totalResourceCount: 1522
    };
  },
  computed: {
    drawerVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    },
    
    // 当前页资源数据
    currentPageResourceData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.allResourceData.slice(start, end);
    }
  },
  watch: {
    visible(newVal) {
      if (newVal && this.detailData && Object.keys(this.detailData).length > 0) {
        this.generateResourceData();
      }
    },
    detailData(newVal) {
      if (newVal && Object.keys(newVal).length > 0 && this.visible) {
        this.generateResourceData();
      }
    }
  },
  methods: {
    // 生成资源数据
    generateResourceData() {
      const resourceData = [];
      const resourceNames = [
        this.$T('地铁物业管理发展有限公司嘻嘻嘻嘻嘻嘻嘻嘻嘻嘻嘻嘻嘻嘻嘻嘻嘻嘻嘻'),
        this.$T('城市能源管理中心'),
        this.$T('工业园区配电站'),
        this.$T('商业综合体供电设施'),
        this.$T('住宅小区变电站')
      ];

      for (let i = 1; i <= this.totalResourceCount; i++) {
        resourceData.push({
          index: i,
          resourceId: `9144030078525478X${i.toString().padStart(3, '0')}`,
          resourceName: resourceNames[Math.floor(Math.random() * resourceNames.length)],
          region: '--',
          capacity: Math.floor(Math.random() * 1000) + 100, // 100-1100 kVA
          directControl: Math.random() > 0.5 // 随机生成是否平台直控
        });
      }

      this.allResourceData = resourceData;
    },

    // 解绑操作
    handleUnbind(row) {
      this.$confirm(
        this.$T('确定要解绑该资源吗？'),
        this.$T('提示'),
        {
          confirmButtonText: this.$T('确定'),
          cancelButtonText: this.$T('取消'),
          type: 'warning'
        }
      ).then(() => {
        // 模拟解绑操作
        const index = this.allResourceData.findIndex(item => item.resourceId === row.resourceId);
        if (index > -1) {
          this.allResourceData.splice(index, 1);
          this.totalResourceCount--;
          
          // 如果当前页没有数据了，回到上一页
          if (this.currentPageResourceData.length === 0 && this.currentPage > 1) {
            this.currentPage--;
          }
        }
        
        this.$message.success(this.$T('解绑成功'));
      }).catch(() => {
        // 取消操作
      });
    },

    // 分页变化
    handlePageChange(page) {
      this.currentPage = page;
    },

    // 页面大小变化
    handlePageSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1; // 重置到第一页
    }
  }
};
</script>

<style lang="scss" scoped>
.detail-drawer {
  .drawer-content {
    @include padding(J4);
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: var(--J4);

    .basic-info-section {
      .info-item {
        display: flex;
        flex-direction: column;
        gap: var(--J1);

        .label {
          @include font_color(T2);
          @include font_size(Aa);
        }

        .value {
          @include font_color(T1);
          @include font_size(Aa);
        }
      }
    }

    .resource-list-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: var(--J3);

      .section-title {
        @include font_color(T1);
        @include font_size(Ab);
        font-weight: 500;
      }

      .resource-table-wrapper {
        flex: 1;
        @include background_color(BG1);
        border-radius: var(--Ra);
        overflow: hidden;

        .resource-table {
          width: 100%;

          .unbind-btn {
            @include font_color(ZS);
            @include font_size(Aa);
            padding: 0;
          }
        }
      }

      .pagination-wrapper {
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .pagination-info {
          margin-right: var(--J3);
          .total-text {
            @include font_color(T2);
            @include font_size(Aa);

            .total-number {
              @include font_color(ZS);
            }
          }
        }

        .pagination-controls {
          display: flex;
          align-items: center;
          gap: var(--J3);

          .page-size-selector {
            .page-size-select {
              width: 100px;
            }
          }
        }
      }
    }
  }
}
</style>
